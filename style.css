/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #00d4ff;
    --secondary-color: #ff6b6b;
    --accent-color: #4ecdc4;
    --dark-bg: #0a0a0a;
    --darker-bg: #050505;
    --light-text: #ffffff;
    --gray-text: #b0b0b0;
    --gradient-1: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-2: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-3: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-ai: linear-gradient(45deg, #00d4ff, #ff6b6b, #4ecdc4);
    --shadow-glow: 0 0 30px rgba(0, 212, 255, 0.3);
    --shadow-intense: 0 0 50px rgba(0, 212, 255, 0.5);
}

body {
    font-family: 'Poppins', sans-serif;
    background: var(--dark-bg);
    color: var(--light-text);
    overflow-x: hidden;
    line-height: 1.6;
}

/* Animated Background */
.animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(45deg, #0a0a0a, #1a1a2e, #16213e);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
    box-shadow: 0 0 10px var(--primary-color);
}

.particle:nth-child(1) {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.particle:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 2s;
}

.particle:nth-child(3) {
    top: 80%;
    left: 40%;
    animation-delay: 4s;
}

.particle:nth-child(4) {
    top: 40%;
    left: 60%;
    animation-delay: 1s;
}

.particle:nth-child(5) {
    top: 10%;
    left: 90%;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(20px) rotate(240deg); }
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.nav-logo {
    font-family: 'Orbitron', monospace;
    font-size: 1.8rem;
    font-weight: 900;
}

.logo-text {
    color: var(--light-text);
}

.logo-ai {
    background: var(--gradient-ai);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { filter: drop-shadow(0 0 5px var(--primary-color)); }
    to { filter: drop-shadow(0 0 15px var(--primary-color)); }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--gray-text);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
    transform: translateY(-2px);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-3);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--light-text);
    margin: 3px 0;
    transition: 0.3s;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Section Titles */
.section-title {
    font-size: 3rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    background: var(--gradient-ai);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: var(--gradient-3);
    border-radius: 2px;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding-top: 80px;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.name-highlight {
    background: var(--gradient-ai);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.typing-container {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    min-height: 60px;
}

.cursor {
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.hero-description {
    font-size: 1.2rem;
    color: var(--gray-text);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

/* Buttons */
.btn-primary, .btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-3);
    color: var(--light-text);
    box-shadow: var(--shadow-glow);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-intense);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--dark-bg);
    transform: translateY(-3px);
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.ai-brain {
    position: relative;
    width: 300px;
    height: 300px;
}

.brain-core {
    width: 150px;
    height: 150px;
    background: var(--gradient-ai);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: rotate 10s linear infinite;
    box-shadow: 0 0 50px rgba(0, 212, 255, 0.5);
}

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

.neural-network {
    position: absolute;
    width: 100%;
    height: 100%;
}

.node {
    width: 20px;
    height: 20px;
    background: var(--accent-color);
    border-radius: 50%;
    position: absolute;
    animation: pulse 2s ease-in-out infinite;
    box-shadow: 0 0 20px var(--accent-color);
}

.node:nth-child(1) {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.node:nth-child(2) {
    top: 20%;
    right: 20%;
    animation-delay: 0.5s;
}

.node:nth-child(3) {
    bottom: 20%;
    left: 20%;
    animation-delay: 1s;
}

.node:nth-child(4) {
    bottom: 20%;
    right: 20%;
    animation-delay: 1.5s;
}

.connection {
    position: absolute;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    animation: flow 3s ease-in-out infinite;
}

.connection:nth-child(5) {
    top: 30%;
    left: 25%;
    width: 50%;
    transform: rotate(45deg);
}

.connection:nth-child(6) {
    top: 50%;
    left: 25%;
    width: 50%;
}

.connection:nth-child(7) {
    bottom: 30%;
    left: 25%;
    width: 50%;
    transform: rotate(-45deg);
}

@keyframes flow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 1; }
}

/* About Section */
.about {
    padding: 100px 0;
    background: rgba(255, 255, 255, 0.02);
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text p {
    font-size: 1.1rem;
    color: var(--gray-text);
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

.about-stats {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    border: 1px solid rgba(0, 212, 255, 0.2);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-glow);
    border-color: var(--primary-color);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--gray-text);
    font-weight: 500;
}

/* Skills Section */
.skills {
    padding: 100px 0;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
}

.skill-category {
    background: rgba(255, 255, 255, 0.03);
    padding: 2rem;
    border-radius: 20px;
    border: 1px solid rgba(0, 212, 255, 0.1);
}

.category-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 2rem;
    text-align: center;
}

.skill-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
}

.skill-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    cursor: pointer;
}

.skill-card:hover {
    transform: translateY(-10px) scale(1.05);
    background: rgba(0, 212, 255, 0.1);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.skill-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.skill-card h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--light-text);
}

.skill-card p {
    font-size: 0.9rem;
    color: var(--gray-text);
}

/* Projects Section */
.projects {
    padding: 100px 0;
    background: rgba(255, 255, 255, 0.02);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.project-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 212, 255, 0.1);
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-glow);
    border-color: var(--primary-color);
}

.project-image {
    height: 200px;
    background: var(--gradient-1);
    position: relative;
    overflow: hidden;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-links {
    display: flex;
    gap: 1rem;
}

.project-link {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark-bg);
    text-decoration: none;
    transition: all 0.3s ease;
}

.project-link:hover {
    transform: scale(1.1);
    background: var(--accent-color);
}

.project-content {
    padding: 2rem;
}

.project-content h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--light-text);
}

.project-content p {
    color: var(--gray-text);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tech-tag {
    background: rgba(0, 212, 255, 0.2);
    color: var(--primary-color);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Contact Section */
.contact {
    padding: 100px 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(0, 212, 255, 0.1);
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateX(10px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--dark-bg);
}

.contact-details h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--light-text);
}

.contact-details a {
    color: var(--gray-text);
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-details a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50px;
    text-decoration: none;
    color: var(--light-text);
    transition: all 0.3s ease;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-3);
    transition: left 0.3s ease;
    z-index: -1;
}

.social-link:hover::before {
    left: 0;
}

.social-link:hover {
    transform: translateY(-3px);
    color: var(--dark-bg);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.social-link i {
    font-size: 1.5rem;
}

.social-link span {
    font-weight: 600;
}

/* Loading Screen */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #0a0a0a, #1a1a2e, #16213e);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    transition: opacity 0.8s ease, visibility 0.8s ease;
}

#loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader-container {
    text-align: center;
}

.ai-loader {
    position: relative;
}

.loader-brain {
    width: 80px;
    height: 80px;
    border: 3px solid rgba(0, 212, 255, 0.3);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem;
    position: relative;
}

.loader-brain::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 2px solid rgba(255, 107, 107, 0.3);
    border-top: 2px solid var(--secondary-color);
    border-radius: 50%;
    animation: spin 0.8s linear infinite reverse;
}

.loader-text {
    color: var(--primary-color);
    font-size: 1.2rem;
    font-weight: 600;
    animation: pulse 1.5s ease-in-out infinite;
}

/* Cursor Trail */
#cursor-trail {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 9998;
}

/* Advanced Animations */
@keyframes matrix-rain {
    0% { transform: translateY(-100vh); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateY(100vh); opacity: 0; }
}

.matrix-particle {
    position: fixed;
    color: var(--primary-color);
    font-family: 'Courier New', monospace;
    font-size: 14px;
    animation: matrix-rain 8s linear infinite;
    z-index: -1;
    pointer-events: none;
}

/* Glitch Effect */
@keyframes glitch {
    0% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
    100% { transform: translate(0); }
}

.glitch-effect {
    animation: glitch 0.3s ease-in-out infinite;
}

/* Neon Glow Animation */
@keyframes neon-glow {
    0%, 100% {
        text-shadow:
            0 0 5px var(--primary-color),
            0 0 10px var(--primary-color),
            0 0 15px var(--primary-color),
            0 0 20px var(--primary-color);
    }
    50% {
        text-shadow:
            0 0 2px var(--primary-color),
            0 0 5px var(--primary-color),
            0 0 8px var(--primary-color),
            0 0 12px var(--primary-color);
    }
}

.neon-text {
    animation: neon-glow 2s ease-in-out infinite alternate;
}

/* Holographic Effect */
@keyframes hologram {
    0% {
        background-position: 0% 50%;
        filter: hue-rotate(0deg);
    }
    50% {
        background-position: 100% 50%;
        filter: hue-rotate(180deg);
    }
    100% {
        background-position: 0% 50%;
        filter: hue-rotate(360deg);
    }
}

.holographic {
    background: linear-gradient(45deg, #ff006e, #8338ec, #3a86ff, #06ffa5, #ffbe0b);
    background-size: 400% 400%;
    animation: hologram 3s ease infinite;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Morphing Shapes */
@keyframes morph {
    0% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
    50% { border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%; }
    100% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
}

.morphing-shape {
    animation: morph 8s ease-in-out infinite;
}

/* Enhanced Button Animations */
.btn-futuristic {
    position: relative;
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 1rem 2rem;
    border-radius: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 600;
}

.btn-futuristic::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.btn-futuristic:hover::before {
    left: 100%;
}

.btn-futuristic:hover {
    color: var(--light-text);
    box-shadow:
        0 0 20px var(--primary-color),
        inset 0 0 20px rgba(0, 212, 255, 0.1);
    transform: translateY(-2px);
}

/* Cyberpunk Grid */
.cyber-grid {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    z-index: -2;
    animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* Floating Elements */
@keyframes float-up {
    0% { transform: translateY(0px) rotate(0deg); opacity: 1; }
    100% { transform: translateY(-1000px) rotate(360deg); opacity: 0; }
}

.floating-element {
    position: fixed;
    pointer-events: none;
    z-index: -1;
    animation: float-up 15s linear infinite;
}

/* Enhanced Skill Cards */
.skill-card-enhanced {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.skill-card-enhanced::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, var(--primary-color), transparent);
    animation: rotate 4s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.skill-card-enhanced::after {
    content: '';
    position: absolute;
    inset: 2px;
    background: var(--dark-bg);
    border-radius: 13px;
    z-index: 1;
}

.skill-card-enhanced:hover::before {
    opacity: 1;
}

.skill-card-enhanced > * {
    position: relative;
    z-index: 2;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hamburger {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .about-content {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .container {
        padding: 0 1rem;
    }

    .ai-brain {
        width: 200px;
        height: 200px;
    }

    .brain-core {
        width: 100px;
        height: 100px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .typing-container {
        font-size: 1.3rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .skill-cards {
        grid-template-columns: 1fr;
    }

    .hero-buttons .btn-primary,
    .hero-buttons .btn-secondary {
        width: 100%;
        justify-content: center;
    }
}
