# 🚀 Prajwal Aswar - Generative AI Engineer <PERSON><PERSON><PERSON>

A mind-blowing, futuristic portfolio showcasing expertise in Generative AI, Machine Learning, and cutting-edge AI technologies.

## ✨ Features

### 🎨 Visual Effects
- **Animated Background**: Dynamic gradient shifting with floating particles
- **Matrix Rain Effect**: Cyberpunk-style character rain animation
- **Cursor Trail**: Advanced canvas-based mouse trail with glow effects
- **Holographic Text**: Color-shifting section titles
- **Glitch Effects**: Periodic glitch animations on hero title
- **Morphing Shapes**: CSS-based shape transformations
- **Particle Systems**: Multiple particle effects throughout the site

### 🎯 Interactive Elements
- **Typing Animation**: Dynamic role/title typing effect
- **Skill Cards**: Interactive cards with hover effects and click animations
- **Project Cards**: 3D tilt effects and overlay animations
- **Smooth Scrolling**: Seamless navigation between sections
- **Responsive Design**: Optimized for all device sizes

### 🔧 Technical Implementation
- **Pure HTML/CSS/JavaScript**: No external frameworks
- **Modern CSS**: Advanced animations, gradients, and effects
- **Canvas API**: Custom cursor trail implementation
- **Intersection Observer**: Scroll-triggered animations
- **Mobile Responsive**: Hamburger menu and mobile optimizations

## 🛠️ Technologies Showcased

### AI & Machine Learning
- Large Language Models (LLMs)
- Retrieval-Augmented Generation (RAG)
- Fine-tuning & Model Optimization
- Agentic AI (CrewAI, LangGraph)
- Natural Language Processing (NLP)
- Deep Learning & Neural Networks

### Development & Frameworks
- Python Programming
- FastAPI for high-performance APIs
- Git Version Control
- Problem Solving & DSA
- Open Source Contributions

## 📁 File Structure

```
portfolio/
├── index.html          # Main HTML structure
├── style.css           # Advanced CSS with animations
├── script.js           # Interactive JavaScript features
└── README.md           # This file
```

## 🚀 Getting Started

### Option 1: Direct File Opening
1. Download all files to a folder
2. Open `index.html` in your web browser
3. Enjoy the mind-blowing experience!

### Option 2: Local Server (Recommended)
1. Navigate to the project folder
2. Run a local server:
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Python 2
   python -m SimpleHTTPServer 8000
   
   # Node.js
   npx serve .
   
   # PHP
   php -S localhost:8000
   ```
3. Open `http://localhost:8000` in your browser

## 🎨 Customization

### Colors & Theme
The portfolio uses CSS custom properties for easy theming:

```css
:root {
    --primary-color: #00d4ff;      /* Main accent color */
    --secondary-color: #ff6b6b;    /* Secondary accent */
    --accent-color: #4ecdc4;       /* Tertiary accent */
    --dark-bg: #0a0a0a;           /* Main background */
    --light-text: #ffffff;         /* Primary text */
    --gray-text: #b0b0b0;         /* Secondary text */
}
```

### Adding Projects
To add your actual projects, update the project cards in `index.html`:

```html
<div class="project-card">
    <div class="project-image">
        <!-- Add your project image/preview -->
    </div>
    <div class="project-content">
        <h3>Your Project Name</h3>
        <p>Project description</p>
        <div class="project-tech">
            <span class="tech-tag">Technology 1</span>
            <span class="tech-tag">Technology 2</span>
        </div>
    </div>
</div>
```

### Modifying Skills
Update the skills section in `index.html` to reflect your expertise:

```html
<div class="skill-card">
    <div class="skill-icon">🤖</div>
    <h4>Your Skill</h4>
    <p>Skill description</p>
</div>
```

## 📱 Mobile Responsiveness

The portfolio is fully responsive with:
- Collapsible hamburger navigation
- Optimized layouts for tablets and phones
- Touch-friendly interactive elements
- Reduced animations for better performance on mobile

## 🌟 Performance Features

- **Optimized Animations**: Hardware-accelerated CSS transforms
- **Efficient Particle Systems**: Automatic cleanup and memory management
- **Lazy Loading**: Scroll-triggered animations to improve initial load
- **Minimal Dependencies**: Pure vanilla JavaScript for fast loading

## 🎯 Browser Compatibility

- ✅ Chrome (recommended)
- ✅ Firefox
- ✅ Safari
- ✅ Edge
- ⚠️ Internet Explorer (limited support)

## 📞 Contact Information

- **Email**: <EMAIL>
- **Phone**: +91 9371278740
- **GitHub**: [github.com/prajwalaswar/prajwalaswar](https://github.com/prajwalaswar/prajwalaswar)
- **LinkedIn**: [linkedin.com/in/prajwal-aswar-5252a12aa](https://www.linkedin.com/in/prajwal-aswar-5252a12aa)

## 🚀 Future Enhancements

Potential additions for even more impact:
- WebGL 3D effects
- AI-powered chatbot integration
- Dynamic project loading from GitHub API
- Blog section with AI-related articles
- Interactive skill assessments
- Dark/Light theme toggle
- Multi-language support

## 📄 License

This portfolio is open source and available under the [MIT License](LICENSE).

---

**Built with ❤️ and cutting-edge web technologies**

*Showcasing the future of AI engineering through innovative web design*
