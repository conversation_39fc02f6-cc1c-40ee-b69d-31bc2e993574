// Typing Animation
const typingText = document.querySelector('.typing-text');
const phrases = [
    'Generative AI Engineer',
    'Machine Learning Expert',
    'RAG Systems Developer',
    'LLM Fine-tuning Specialist',
    'Agentic AI Creator',
    'Deep Learning Enthusiast',
    'NLP Engineer',
    'AI Innovation Leader'
];

let phraseIndex = 0;
let charIndex = 0;
let isDeleting = false;
let typingSpeed = 100;

function typeEffect() {
    const currentPhrase = phrases[phraseIndex];
    
    if (isDeleting) {
        typingText.textContent = currentPhrase.substring(0, charIndex - 1);
        charIndex--;
        typingSpeed = 50;
    } else {
        typingText.textContent = currentPhrase.substring(0, charIndex + 1);
        charIndex++;
        typingSpeed = 100;
    }
    
    if (!isDeleting && charIndex === currentPhrase.length) {
        isDeleting = true;
        typingSpeed = 2000; // Pause at end
    } else if (isDeleting && charIndex === 0) {
        isDeleting = false;
        phraseIndex = (phraseIndex + 1) % phrases.length;
        typingSpeed = 500; // Pause before next phrase
    }
    
    setTimeout(typeEffect, typingSpeed);
}

// Start typing animation when page loads
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(typeEffect, 1000);
});

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Navbar scroll effect
window.addEventListener('scroll', () => {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 100) {
        navbar.style.background = 'rgba(10, 10, 10, 0.98)';
        navbar.style.boxShadow = '0 2px 20px rgba(0, 212, 255, 0.1)';
    } else {
        navbar.style.background = 'rgba(10, 10, 10, 0.95)';
        navbar.style.boxShadow = 'none';
    }
});

// Mobile menu toggle
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

hamburger.addEventListener('click', () => {
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-link').forEach(n => n.addEventListener('click', () => {
    hamburger.classList.remove('active');
    navMenu.classList.remove('active');
}));

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for scroll animations
document.addEventListener('DOMContentLoaded', () => {
    const animateElements = document.querySelectorAll('.skill-card, .project-card, .stat-card, .contact-item');
    
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'all 0.6s ease';
        observer.observe(el);
    });
});

// Particle system enhancement
function createParticle() {
    const particle = document.createElement('div');
    particle.className = 'dynamic-particle';
    particle.style.cssText = `
        position: fixed;
        width: 2px;
        height: 2px;
        background: #00d4ff;
        border-radius: 50%;
        pointer-events: none;
        z-index: -1;
        box-shadow: 0 0 6px #00d4ff;
    `;
    
    const startX = Math.random() * window.innerWidth;
    const startY = window.innerHeight + 10;
    const endY = -10;
    const duration = Math.random() * 3000 + 2000;
    
    particle.style.left = startX + 'px';
    particle.style.top = startY + 'px';
    
    document.body.appendChild(particle);
    
    particle.animate([
        { transform: `translateY(0px)`, opacity: 0 },
        { transform: `translateY(-${window.innerHeight + 20}px)`, opacity: 1 },
        { transform: `translateY(-${window.innerHeight + 40}px)`, opacity: 0 }
    ], {
        duration: duration,
        easing: 'linear'
    }).onfinish = () => {
        particle.remove();
    };
}

// Create particles periodically
setInterval(createParticle, 300);

// Skill card hover effects
document.querySelectorAll('.skill-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.background = 'rgba(0, 212, 255, 0.15)';
        this.style.borderColor = '#00d4ff';
        
        // Add ripple effect
        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: absolute;
            border-radius: 50%;
            background: rgba(0, 212, 255, 0.3);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;
        
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = (rect.width / 2 - size / 2) + 'px';
        ripple.style.top = (rect.height / 2 - size / 2) + 'px';
        
        this.style.position = 'relative';
        this.appendChild(ripple);
        
        setTimeout(() => ripple.remove(), 600);
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.background = 'rgba(255, 255, 255, 0.05)';
        this.style.borderColor = 'transparent';
    });
});

// Add CSS for ripple animation
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .nav-menu.active {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: rgba(10, 10, 10, 0.98);
        backdrop-filter: blur(10px);
        padding: 2rem;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    }
    
    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }
    
    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }
    
    @media (max-width: 768px) {
        .nav-menu {
            display: none;
        }
    }
`;
document.head.appendChild(style);

// Project card 3D tilt effect
document.querySelectorAll('.project-card').forEach(card => {
    card.addEventListener('mousemove', function(e) {
        const rect = this.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        
        const rotateX = (y - centerY) / 10;
        const rotateY = (centerX - x) / 10;
        
        this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-10px)`;
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateY(0)';
    });
});

// Counter animation for stats
function animateCounter(element, target) {
    let current = 0;
    const increment = target / 100;
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current) + '+';
    }, 20);
}

// Trigger counter animation when stats come into view
const statsObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const number = entry.target.querySelector('.stat-number');
            const target = parseInt(number.textContent);
            animateCounter(number, target);
            statsObserver.unobserve(entry.target);
        }
    });
}, { threshold: 0.5 });

document.querySelectorAll('.stat-card').forEach(card => {
    statsObserver.observe(card);
});

// Add floating animation to hero elements
document.addEventListener('DOMContentLoaded', () => {
    const heroElements = document.querySelectorAll('.hero-title, .typing-container, .hero-description');
    
    heroElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.transition = 'all 0.8s ease';
        
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, 200 * (index + 1));
    });
});

// Enhanced scroll reveal for sections
const revealElements = document.querySelectorAll('.section-title, .about-text, .contact-info');

const revealObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, { threshold: 0.2 });

revealElements.forEach(element => {
    element.style.opacity = '0';
    element.style.transform = 'translateY(50px)';
    element.style.transition = 'all 0.8s ease';
    revealObserver.observe(element);
});

// Add glow effect to buttons on hover
document.querySelectorAll('.btn-primary, .btn-secondary').forEach(button => {
    button.addEventListener('mouseenter', function() {
        this.style.boxShadow = '0 0 30px rgba(0, 212, 255, 0.6), 0 0 60px rgba(0, 212, 255, 0.4)';
    });
    
    button.addEventListener('mouseleave', function() {
        if (this.classList.contains('btn-primary')) {
            this.style.boxShadow = '0 0 30px rgba(0, 212, 255, 0.3)';
        } else {
            this.style.boxShadow = 'none';
        }
    });
});

// Parallax effect for hero section
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const parallaxElements = document.querySelectorAll('.ai-brain, .hero-visual');
    
    parallaxElements.forEach(element => {
        const speed = 0.5;
        element.style.transform = `translateY(${scrolled * speed}px)`;
    });
});

// Enhanced loading animation
window.addEventListener('load', () => {
    const loadingScreen = document.getElementById('loading-screen');
    document.body.style.overflow = 'hidden';

    // Simulate loading time
    setTimeout(() => {
        loadingScreen.classList.add('hidden');
        document.body.style.overflow = 'auto';

        // Remove loading screen after transition
        setTimeout(() => {
            loadingScreen.remove();
        }, 800);
    }, 2000);
});

// Advanced cursor trail with canvas
const canvas = document.getElementById('cursor-trail');
const ctx = canvas.getContext('2d');
let trail = [];
const maxTrailLength = 50;

function resizeCanvas() {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
}

window.addEventListener('resize', resizeCanvas);
resizeCanvas();

document.addEventListener('mousemove', (e) => {
    trail.push({
        x: e.clientX,
        y: e.clientY,
        life: 1.0
    });

    if (trail.length > maxTrailLength) {
        trail.shift();
    }
});

function drawTrail() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    for (let i = 0; i < trail.length; i++) {
        const point = trail[i];
        const alpha = point.life * (i / trail.length);
        const size = (i / trail.length) * 8;

        ctx.beginPath();
        ctx.arc(point.x, point.y, size, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(0, 212, 255, ${alpha})`;
        ctx.fill();

        // Create glow effect
        ctx.beginPath();
        ctx.arc(point.x, point.y, size * 2, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(0, 212, 255, ${alpha * 0.2})`;
        ctx.fill();

        point.life -= 0.02;
    }

    // Remove dead points
    trail = trail.filter(point => point.life > 0);

    requestAnimationFrame(drawTrail);
}

drawTrail();

// Add spin animation for loader
const loaderStyle = document.createElement('style');
loaderStyle.textContent = `
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
`;
document.head.appendChild(loaderStyle);

// Matrix rain effect
function createMatrixRain() {
    const characters = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
    const matrixChar = document.createElement('div');
    matrixChar.className = 'matrix-particle';
    matrixChar.textContent = characters[Math.floor(Math.random() * characters.length)];
    matrixChar.style.left = Math.random() * window.innerWidth + 'px';
    matrixChar.style.animationDuration = (Math.random() * 3 + 5) + 's';
    matrixChar.style.opacity = Math.random() * 0.5 + 0.3;

    document.body.appendChild(matrixChar);

    setTimeout(() => {
        matrixChar.remove();
    }, 8000);
}

// Create matrix rain periodically
setInterval(createMatrixRain, 200);

// Cyberpunk grid background
function addCyberGrid() {
    const grid = document.createElement('div');
    grid.className = 'cyber-grid';
    document.body.appendChild(grid);
}

// Add cyber grid on load
document.addEventListener('DOMContentLoaded', addCyberGrid);

// Enhanced mouse trail effect
let mouseTrail = [];
const maxMouseTrailLength = 20;

document.addEventListener('mousemove', (e) => {
    mouseTrail.push({ x: e.clientX, y: e.clientY, time: Date.now() });

    if (mouseTrail.length > maxMouseTrailLength) {
        mouseTrail.shift();
    }

    // Create trail particle
    const particle = document.createElement('div');
    particle.style.cssText = `
        position: fixed;
        width: 4px;
        height: 4px;
        background: radial-gradient(circle, #00d4ff, transparent);
        border-radius: 50%;
        pointer-events: none;
        z-index: 9999;
        left: ${e.clientX - 2}px;
        top: ${e.clientY - 2}px;
        animation: fadeOut 0.5s ease-out forwards;
    `;

    document.body.appendChild(particle);

    setTimeout(() => particle.remove(), 500);
});

// Add fadeOut animation
const trailStyle = document.createElement('style');
trailStyle.textContent = `
    @keyframes fadeOut {
        0% { opacity: 1; transform: scale(1); }
        100% { opacity: 0; transform: scale(0); }
    }
`;
document.head.appendChild(trailStyle);

// Floating geometric shapes
function createFloatingShape() {
    const shapes = ['◆', '▲', '●', '■', '★'];
    const shape = document.createElement('div');
    shape.className = 'floating-element';
    shape.textContent = shapes[Math.floor(Math.random() * shapes.length)];
    shape.style.cssText = `
        left: ${Math.random() * window.innerWidth}px;
        top: ${window.innerHeight + 50}px;
        font-size: ${Math.random() * 20 + 10}px;
        color: rgba(0, 212, 255, ${Math.random() * 0.5 + 0.2});
    `;

    document.body.appendChild(shape);

    setTimeout(() => shape.remove(), 15000);
}

// Create floating shapes periodically
setInterval(createFloatingShape, 2000);

// Enhanced glitch effect for hero title
function addGlitchEffect() {
    const heroTitle = document.querySelector('.hero-title');
    if (heroTitle) {
        setInterval(() => {
            heroTitle.classList.add('glitch-effect');
            setTimeout(() => {
                heroTitle.classList.remove('glitch-effect');
            }, 300);
        }, 5000);
    }
}

// Add glitch effect on load
document.addEventListener('DOMContentLoaded', addGlitchEffect);

// Holographic text effect for section titles
document.addEventListener('DOMContentLoaded', () => {
    const sectionTitles = document.querySelectorAll('.section-title');
    sectionTitles.forEach(title => {
        title.classList.add('holographic');
    });
});

// Interactive skill cards with enhanced effects
document.querySelectorAll('.skill-card').forEach(card => {
    card.addEventListener('click', function() {
        // Add pulse animation
        this.style.animation = 'pulse 0.6s ease-in-out';

        // Create explosion effect
        for (let i = 0; i < 8; i++) {
            const spark = document.createElement('div');
            spark.style.cssText = `
                position: absolute;
                width: 4px;
                height: 4px;
                background: #00d4ff;
                border-radius: 50%;
                pointer-events: none;
                z-index: 1000;
            `;

            const rect = this.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;

            spark.style.left = centerX + 'px';
            spark.style.top = centerY + 'px';

            document.body.appendChild(spark);

            const angle = (i / 8) * Math.PI * 2;
            const distance = 100;
            const endX = centerX + Math.cos(angle) * distance;
            const endY = centerY + Math.sin(angle) * distance;

            spark.animate([
                { transform: 'translate(0, 0) scale(1)', opacity: 1 },
                { transform: `translate(${endX - centerX}px, ${endY - centerY}px) scale(0)`, opacity: 0 }
            ], {
                duration: 800,
                easing: 'ease-out'
            }).onfinish = () => spark.remove();
        }

        setTimeout(() => {
            this.style.animation = '';
        }, 600);
    });
});

// Dynamic background color shifting
function shiftBackgroundColors() {
    const colors = [
        'linear-gradient(45deg, #0a0a0a, #1a1a2e, #16213e)',
        'linear-gradient(45deg, #0a0a0a, #2e1a1a, #3e1621)',
        'linear-gradient(45deg, #0a0a0a, #1a2e1a, #213e16)',
        'linear-gradient(45deg, #0a0a0a, #1a1a2e, #16213e)'
    ];

    let currentIndex = 0;
    const animatedBg = document.querySelector('.animated-bg');

    setInterval(() => {
        currentIndex = (currentIndex + 1) % colors.length;
        animatedBg.style.background = colors[currentIndex];
    }, 10000);
}

// Start background color shifting
document.addEventListener('DOMContentLoaded', shiftBackgroundColors);
