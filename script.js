// Typing Animation
const typingText = document.querySelector('.typing-text');
const phrases = [
    'Generative AI Engineer',
    'Machine Learning Expert',
    'RAG Systems Developer',
    'LLM Fine-tuning Specialist',
    'Agentic AI Creator',
    'Deep Learning Enthusiast',
    'NLP Engineer',
    'AI Innovation Leader'
];

let phraseIndex = 0;
let charIndex = 0;
let isDeleting = false;
let typingSpeed = 100;

function typeEffect() {
    const currentPhrase = phrases[phraseIndex];
    
    if (isDeleting) {
        typingText.textContent = currentPhrase.substring(0, charIndex - 1);
        charIndex--;
        typingSpeed = 50;
    } else {
        typingText.textContent = currentPhrase.substring(0, charIndex + 1);
        charIndex++;
        typingSpeed = 100;
    }
    
    if (!isDeleting && charIndex === currentPhrase.length) {
        isDeleting = true;
        typingSpeed = 2000; // Pause at end
    } else if (isDeleting && charIndex === 0) {
        isDeleting = false;
        phraseIndex = (phraseIndex + 1) % phrases.length;
        typingSpeed = 500; // Pause before next phrase
    }
    
    setTimeout(typeEffect, typingSpeed);
}

// Start typing animation when page loads
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(typeEffect, 1000);
});

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Navbar scroll effect
window.addEventListener('scroll', () => {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 100) {
        navbar.style.background = 'rgba(10, 10, 10, 0.98)';
        navbar.style.boxShadow = '0 2px 20px rgba(0, 212, 255, 0.1)';
    } else {
        navbar.style.background = 'rgba(10, 10, 10, 0.95)';
        navbar.style.boxShadow = 'none';
    }
});

// Mobile menu toggle
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

hamburger.addEventListener('click', () => {
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-link').forEach(n => n.addEventListener('click', () => {
    hamburger.classList.remove('active');
    navMenu.classList.remove('active');
}));

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for scroll animations
document.addEventListener('DOMContentLoaded', () => {
    const animateElements = document.querySelectorAll('.skill-card, .project-card, .stat-card, .contact-item');
    
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'all 0.6s ease';
        observer.observe(el);
    });
});

// Particle system enhancement
function createParticle() {
    const particle = document.createElement('div');
    particle.className = 'dynamic-particle';
    particle.style.cssText = `
        position: fixed;
        width: 2px;
        height: 2px;
        background: #00d4ff;
        border-radius: 50%;
        pointer-events: none;
        z-index: -1;
        box-shadow: 0 0 6px #00d4ff;
    `;
    
    const startX = Math.random() * window.innerWidth;
    const startY = window.innerHeight + 10;
    const endY = -10;
    const duration = Math.random() * 3000 + 2000;
    
    particle.style.left = startX + 'px';
    particle.style.top = startY + 'px';
    
    document.body.appendChild(particle);
    
    particle.animate([
        { transform: `translateY(0px)`, opacity: 0 },
        { transform: `translateY(-${window.innerHeight + 20}px)`, opacity: 1 },
        { transform: `translateY(-${window.innerHeight + 40}px)`, opacity: 0 }
    ], {
        duration: duration,
        easing: 'linear'
    }).onfinish = () => {
        particle.remove();
    };
}

// Create particles periodically
setInterval(createParticle, 300);

// Skill card hover effects
document.querySelectorAll('.skill-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.background = 'rgba(0, 212, 255, 0.15)';
        this.style.borderColor = '#00d4ff';
        
        // Add ripple effect
        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: absolute;
            border-radius: 50%;
            background: rgba(0, 212, 255, 0.3);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;
        
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = (rect.width / 2 - size / 2) + 'px';
        ripple.style.top = (rect.height / 2 - size / 2) + 'px';
        
        this.style.position = 'relative';
        this.appendChild(ripple);
        
        setTimeout(() => ripple.remove(), 600);
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.background = 'rgba(255, 255, 255, 0.05)';
        this.style.borderColor = 'transparent';
    });
});

// Add CSS for ripple animation
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .nav-menu.active {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: rgba(10, 10, 10, 0.98);
        backdrop-filter: blur(10px);
        padding: 2rem;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    }
    
    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }
    
    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }
    
    @media (max-width: 768px) {
        .nav-menu {
            display: none;
        }
    }
`;
document.head.appendChild(style);

// Project card 3D tilt effect
document.querySelectorAll('.project-card').forEach(card => {
    card.addEventListener('mousemove', function(e) {
        const rect = this.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        
        const rotateX = (y - centerY) / 10;
        const rotateY = (centerX - x) / 10;
        
        this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-10px)`;
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateY(0)';
    });
});

// Counter animation for stats
function animateCounter(element, target) {
    let current = 0;
    const increment = target / 100;
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current) + '+';
    }, 20);
}

// Trigger counter animation when stats come into view
const statsObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const number = entry.target.querySelector('.stat-number');
            const target = parseInt(number.textContent);
            animateCounter(number, target);
            statsObserver.unobserve(entry.target);
        }
    });
}, { threshold: 0.5 });

document.querySelectorAll('.stat-card').forEach(card => {
    statsObserver.observe(card);
});

// Add floating animation to hero elements
document.addEventListener('DOMContentLoaded', () => {
    const heroElements = document.querySelectorAll('.hero-title, .typing-container, .hero-description');
    
    heroElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.transition = 'all 0.8s ease';
        
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, 200 * (index + 1));
    });
});

// Enhanced scroll reveal for sections
const revealElements = document.querySelectorAll('.section-title, .about-text, .contact-info');

const revealObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, { threshold: 0.2 });

revealElements.forEach(element => {
    element.style.opacity = '0';
    element.style.transform = 'translateY(50px)';
    element.style.transition = 'all 0.8s ease';
    revealObserver.observe(element);
});

// Add glow effect to buttons on hover
document.querySelectorAll('.btn-primary, .btn-secondary').forEach(button => {
    button.addEventListener('mouseenter', function() {
        this.style.boxShadow = '0 0 30px rgba(0, 212, 255, 0.6), 0 0 60px rgba(0, 212, 255, 0.4)';
    });
    
    button.addEventListener('mouseleave', function() {
        if (this.classList.contains('btn-primary')) {
            this.style.boxShadow = '0 0 30px rgba(0, 212, 255, 0.3)';
        } else {
            this.style.boxShadow = 'none';
        }
    });
});

// Parallax effect for hero section
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const parallaxElements = document.querySelectorAll('.ai-brain, .hero-visual');
    
    parallaxElements.forEach(element => {
        const speed = 0.5;
        element.style.transform = `translateY(${scrolled * speed}px)`;
    });
});

// Enhanced loading animation
window.addEventListener('load', () => {
    const loadingScreen = document.getElementById('loading-screen');
    document.body.style.overflow = 'hidden';

    // Simulate loading time
    setTimeout(() => {
        loadingScreen.classList.add('hidden');
        document.body.style.overflow = 'auto';

        // Remove loading screen after transition
        setTimeout(() => {
            loadingScreen.remove();
        }, 800);
    }, 2000);
});

// Advanced cursor trail with canvas
const canvas = document.getElementById('cursor-trail');
const ctx = canvas.getContext('2d');
let trail = [];
const maxTrailLength = 50;

function resizeCanvas() {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
}

window.addEventListener('resize', resizeCanvas);
resizeCanvas();

document.addEventListener('mousemove', (e) => {
    trail.push({
        x: e.clientX,
        y: e.clientY,
        life: 1.0
    });

    if (trail.length > maxTrailLength) {
        trail.shift();
    }
});

function drawTrail() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    for (let i = 0; i < trail.length; i++) {
        const point = trail[i];
        const alpha = point.life * (i / trail.length);
        const size = (i / trail.length) * 8;

        ctx.beginPath();
        ctx.arc(point.x, point.y, size, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(0, 212, 255, ${alpha})`;
        ctx.fill();

        // Create glow effect
        ctx.beginPath();
        ctx.arc(point.x, point.y, size * 2, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(0, 212, 255, ${alpha * 0.2})`;
        ctx.fill();

        point.life -= 0.02;
    }

    // Remove dead points
    trail = trail.filter(point => point.life > 0);

    requestAnimationFrame(drawTrail);
}

drawTrail();

// Add spin animation for loader
const loaderStyle = document.createElement('style');
loaderStyle.textContent = `
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
`;
document.head.appendChild(loaderStyle);

// Matrix rain effect
function createMatrixRain() {
    const characters = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
    const matrixChar = document.createElement('div');
    matrixChar.className = 'matrix-particle';
    matrixChar.textContent = characters[Math.floor(Math.random() * characters.length)];
    matrixChar.style.left = Math.random() * window.innerWidth + 'px';
    matrixChar.style.animationDuration = (Math.random() * 3 + 5) + 's';
    matrixChar.style.opacity = Math.random() * 0.5 + 0.3;

    document.body.appendChild(matrixChar);

    setTimeout(() => {
        matrixChar.remove();
    }, 8000);
}

// Create matrix rain periodically
setInterval(createMatrixRain, 200);

// Cyberpunk grid background
function addCyberGrid() {
    const grid = document.createElement('div');
    grid.className = 'cyber-grid';
    document.body.appendChild(grid);
}

// Add cyber grid on load
document.addEventListener('DOMContentLoaded', addCyberGrid);

// Enhanced mouse trail effect
let mouseTrail = [];
const maxMouseTrailLength = 20;

document.addEventListener('mousemove', (e) => {
    mouseTrail.push({ x: e.clientX, y: e.clientY, time: Date.now() });

    if (mouseTrail.length > maxMouseTrailLength) {
        mouseTrail.shift();
    }

    // Create trail particle
    const particle = document.createElement('div');
    particle.style.cssText = `
        position: fixed;
        width: 4px;
        height: 4px;
        background: radial-gradient(circle, #00d4ff, transparent);
        border-radius: 50%;
        pointer-events: none;
        z-index: 9999;
        left: ${e.clientX - 2}px;
        top: ${e.clientY - 2}px;
        animation: fadeOut 0.5s ease-out forwards;
    `;

    document.body.appendChild(particle);

    setTimeout(() => particle.remove(), 500);
});

// Add fadeOut animation
const trailStyle = document.createElement('style');
trailStyle.textContent = `
    @keyframes fadeOut {
        0% { opacity: 1; transform: scale(1); }
        100% { opacity: 0; transform: scale(0); }
    }
`;
document.head.appendChild(trailStyle);

// Floating geometric shapes
function createFloatingShape() {
    const shapes = ['◆', '▲', '●', '■', '★'];
    const shape = document.createElement('div');
    shape.className = 'floating-element';
    shape.textContent = shapes[Math.floor(Math.random() * shapes.length)];
    shape.style.cssText = `
        left: ${Math.random() * window.innerWidth}px;
        top: ${window.innerHeight + 50}px;
        font-size: ${Math.random() * 20 + 10}px;
        color: rgba(0, 212, 255, ${Math.random() * 0.5 + 0.2});
    `;

    document.body.appendChild(shape);

    setTimeout(() => shape.remove(), 15000);
}

// Create floating shapes periodically
setInterval(createFloatingShape, 2000);

// Enhanced glitch effect for hero title
function addGlitchEffect() {
    const heroTitle = document.querySelector('.hero-title');
    if (heroTitle) {
        setInterval(() => {
            heroTitle.classList.add('glitch-effect');
            setTimeout(() => {
                heroTitle.classList.remove('glitch-effect');
            }, 300);
        }, 5000);
    }
}

// Add glitch effect on load
document.addEventListener('DOMContentLoaded', addGlitchEffect);

// Holographic text effect for section titles
document.addEventListener('DOMContentLoaded', () => {
    const sectionTitles = document.querySelectorAll('.section-title');
    sectionTitles.forEach(title => {
        title.classList.add('holographic');
    });
});

// Interactive skill cards with enhanced effects
document.querySelectorAll('.skill-card').forEach(card => {
    card.addEventListener('click', function() {
        // Add pulse animation
        this.style.animation = 'pulse 0.6s ease-in-out';

        // Create explosion effect
        for (let i = 0; i < 8; i++) {
            const spark = document.createElement('div');
            spark.style.cssText = `
                position: absolute;
                width: 4px;
                height: 4px;
                background: #00d4ff;
                border-radius: 50%;
                pointer-events: none;
                z-index: 1000;
            `;

            const rect = this.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;

            spark.style.left = centerX + 'px';
            spark.style.top = centerY + 'px';

            document.body.appendChild(spark);

            const angle = (i / 8) * Math.PI * 2;
            const distance = 100;
            const endX = centerX + Math.cos(angle) * distance;
            const endY = centerY + Math.sin(angle) * distance;

            spark.animate([
                { transform: 'translate(0, 0) scale(1)', opacity: 1 },
                { transform: `translate(${endX - centerX}px, ${endY - centerY}px) scale(0)`, opacity: 0 }
            ], {
                duration: 800,
                easing: 'ease-out'
            }).onfinish = () => spark.remove();
        }

        setTimeout(() => {
            this.style.animation = '';
        }, 600);
    });
});

// Dynamic background color shifting
function shiftBackgroundColors() {
    const colors = [
        'linear-gradient(45deg, #0a0a0a, #1a1a2e, #16213e)',
        'linear-gradient(45deg, #0a0a0a, #2e1a1a, #3e1621)',
        'linear-gradient(45deg, #0a0a0a, #1a2e1a, #213e16)',
        'linear-gradient(45deg, #0a0a0a, #1a1a2e, #16213e)'
    ];

    let currentIndex = 0;
    const animatedBg = document.querySelector('.animated-bg');

    setInterval(() => {
        currentIndex = (currentIndex + 1) % colors.length;
        animatedBg.style.background = colors[currentIndex];
    }, 10000);
}

// Start background color shifting
document.addEventListener('DOMContentLoaded', shiftBackgroundColors);

// Chatbot functionality
class AIAssistant {
    constructor() {
        this.isOpen = false;
        this.responses = {
            skills: [
                "Prajwal is a skilled Gen AI engineer with expertise in:",
                "🤖 FastAPI - Building robust AI APIs",
                "🔍 RAG (Retrieval Augmented Generation) - Advanced search systems",
                "🎯 Fine-tuning - Customizing AI models",
                "🤝 Agentic AI - Building intelligent autonomous systems",
                "⚡ CrewAI & LangGraph - Multi-agent frameworks",
                "📊 NLP, ML, DL - Core AI technologies",
                "🐍 Python - Primary programming language",
                "🔧 Git - Version control and collaboration",
                "💡 Problem solving & Data Structures"
            ],
            experience: [
                "Prajwal's professional journey:",
                "🎓 Final year BE Computer Science student at SPPU",
                "💼 Currently interning at DataSmith AI",
                "🏠 Based in Amravati, Maharashtra",
                "🚀 22 years old with a passion for Generative AI",
                "🌟 Specializes in building AI-powered solutions",
                "📈 Experienced in end-to-end AI project development",
                "🔬 Research-oriented approach to AI innovation"
            ],
            education: [
                "Prajwal's educational background:",
                "🎓 Bachelor of Engineering in Computer Science",
                "🏫 Savitribai Phule Pune University (SPPU)",
                "📅 Final year student (2025 graduate)",
                "🏆 Strong foundation in computer science fundamentals",
                "💻 Specialized focus on Generative AI and Machine Learning",
                "📚 Continuous learner in cutting-edge AI technologies"
            ],
            contact: [
                "Ready to connect with Prajwal? Here's how:",
                "📧 Email: <EMAIL>",
                "📱 Phone: +91 9371278740",
                "💼 LinkedIn: www.linkedin.com/in/prajwal-aswar-5252a12aa",
                "🐙 GitHub: github.com/prajwalaswar/prajwalaswar",
                "🌟 Feel free to reach out for collaborations, opportunities, or just to chat about AI!"
            ],
            projects: [
                "Prajwal has worked on exciting AI projects:",
                "🤖 RAG-based intelligent search systems",
                "🔧 Custom AI model fine-tuning projects",
                "⚡ FastAPI-powered AI applications",
                "🤝 Multi-agent AI systems using CrewAI",
                "📊 NLP and data analysis solutions",
                "🚀 End-to-end ML/DL pipelines",
                "Check out his GitHub for detailed project showcases!"
            ],
            personality: [
                "What makes Prajwal special:",
                "🌟 Passionate about pushing AI boundaries",
                "💡 Creative problem solver with innovative thinking",
                "🤝 Great team player and collaborator",
                "📚 Continuous learner and tech enthusiast",
                "🎯 Goal-oriented with attention to detail",
                "😊 Friendly and approachable personality",
                "🚀 Always excited about new AI challenges!"
            ]
        };

        this.greetings = [
            "Hey there! 👋 What would you like to know about Prajwal?",
            "Hello! 😊 I'm here to tell you all about Prajwal's AI expertise!",
            "Hi! 🤖 Ready to learn about Prajwal's amazing journey in AI?",
            "Welcome! ✨ Ask me anything about Prajwal's skills and experience!"
        ];

        this.fallbackResponses = [
            "That's an interesting question! 🤔 While I specialize in Prajwal's professional info, you can ask me about his skills, experience, education, or contact details!",
            "I'd love to help! 😊 Try asking about Prajwal's AI expertise, projects, or how to get in touch with him!",
            "Great question! 🌟 I'm here to share info about Prajwal's background in Generative AI. What specific area interests you?",
            "Thanks for asking! 💫 I can tell you about Prajwal's skills, experience, education, or contact information. What would you like to know?"
        ];

        this.init();
    }

    init() {
        this.chatbotToggle = document.getElementById('chatbot-toggle');
        this.chatbotWindow = document.getElementById('chatbot-window');
        this.chatbotClose = document.getElementById('chatbot-close');
        this.chatbotInput = document.getElementById('chatbot-input');
        this.chatbotSend = document.getElementById('chatbot-send');
        this.chatbotMessages = document.getElementById('chatbot-messages');
        this.quickActions = document.querySelectorAll('.quick-action');

        this.bindEvents();
    }

    bindEvents() {
        this.chatbotToggle.addEventListener('click', () => this.toggleChat());
        this.chatbotClose.addEventListener('click', () => this.closeChat());
        this.chatbotSend.addEventListener('click', () => this.sendMessage());
        this.chatbotInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.sendMessage();
        });

        this.quickActions.forEach(action => {
            action.addEventListener('click', () => {
                const question = action.dataset.question;
                this.addUserMessage(question);
                this.processMessage(question);
            });
        });
    }

    toggleChat() {
        if (this.isOpen) {
            this.closeChat();
        } else {
            this.openChat();
        }
    }

    openChat() {
        this.isOpen = true;
        this.chatbotWindow.classList.add('active');
        this.chatbotInput.focus();

        // Hide notification
        const notification = document.getElementById('chat-notification');
        notification.style.display = 'none';
    }

    closeChat() {
        this.isOpen = false;
        this.chatbotWindow.classList.remove('active');
    }

    sendMessage() {
        const message = this.chatbotInput.value.trim();
        if (!message) return;

        this.addUserMessage(message);
        this.chatbotInput.value = '';

        // Show typing indicator
        this.showTypingIndicator();

        // Process message after a delay
        setTimeout(() => {
            this.hideTypingIndicator();
            this.processMessage(message);
        }, 1000 + Math.random() * 1000);
    }

    addUserMessage(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message user-message';
        messageDiv.innerHTML = `
            <div class="message-avatar">
                <span>P</span>
            </div>
            <div class="message-content">
                <p>${message}</p>
            </div>
        `;
        this.chatbotMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    addBotMessage(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message bot-message';
        messageDiv.innerHTML = `
            <div class="message-avatar">
                <div class="avatar-face-tiny">
                    <div class="avatar-eyes-tiny">
                        <div class="eye-tiny">
                            <div class="pupil-tiny"></div>
                        </div>
                        <div class="eye-tiny">
                            <div class="pupil-tiny"></div>
                        </div>
                    </div>
                    <div class="avatar-mouth-tiny"></div>
                </div>
            </div>
            <div class="message-content">
                <p>${message}</p>
            </div>
        `;
        this.chatbotMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message bot-message typing-message';
        typingDiv.innerHTML = `
            <div class="message-avatar">
                <div class="avatar-face-tiny">
                    <div class="avatar-eyes-tiny">
                        <div class="eye-tiny">
                            <div class="pupil-tiny"></div>
                        </div>
                        <div class="eye-tiny">
                            <div class="pupil-tiny"></div>
                        </div>
                    </div>
                    <div class="avatar-mouth-tiny"></div>
                </div>
            </div>
            <div class="message-content">
                <div class="typing-indicator">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        `;
        this.chatbotMessages.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const typingMessage = this.chatbotMessages.querySelector('.typing-message');
        if (typingMessage) {
            typingMessage.remove();
        }
    }

    processMessage(message) {
        const lowerMessage = message.toLowerCase();
        let response = '';

        // Determine response based on keywords
        if (this.containsKeywords(lowerMessage, ['skill', 'technology', 'tech', 'programming', 'coding', 'fastapi', 'rag', 'ai', 'python'])) {
            response = this.responses.skills.join('\n');
        } else if (this.containsKeywords(lowerMessage, ['experience', 'work', 'job', 'intern', 'career', 'professional'])) {
            response = this.responses.experience.join('\n');
        } else if (this.containsKeywords(lowerMessage, ['education', 'study', 'college', 'university', 'degree', 'student'])) {
            response = this.responses.education.join('\n');
        } else if (this.containsKeywords(lowerMessage, ['contact', 'reach', 'email', 'phone', 'linkedin', 'github', 'connect'])) {
            response = this.responses.contact.join('\n');
        } else if (this.containsKeywords(lowerMessage, ['project', 'portfolio', 'work', 'build', 'create', 'develop'])) {
            response = this.responses.projects.join('\n');
        } else if (this.containsKeywords(lowerMessage, ['personality', 'person', 'character', 'about', 'who', 'describe'])) {
            response = this.responses.personality.join('\n');
        } else if (this.containsKeywords(lowerMessage, ['hello', 'hi', 'hey', 'greet'])) {
            response = this.greetings[Math.floor(Math.random() * this.greetings.length)];
        } else {
            response = this.fallbackResponses[Math.floor(Math.random() * this.fallbackResponses.length)];
        }

        // Split response into multiple messages if it's long
        const lines = response.split('\n');
        lines.forEach((line, index) => {
            setTimeout(() => {
                this.addBotMessage(line);
            }, index * 500);
        });
    }

    containsKeywords(message, keywords) {
        return keywords.some(keyword => message.includes(keyword));
    }

    scrollToBottom() {
        this.chatbotMessages.scrollTop = this.chatbotMessages.scrollHeight;
    }
}

// Advanced 3D Effects and Enhancements
function createFloatingParticles() {
    const heroSection = document.querySelector('.hero-section');
    const particlesContainer = document.createElement('div');
    particlesContainer.className = 'floating-particles';
    heroSection.appendChild(particlesContainer);

    // Create 50 floating particles
    for (let i = 0; i < 50; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';

        // Random starting position
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 20 + 's';
        particle.style.animationDuration = (20 + Math.random() * 10) + 's';

        particlesContainer.appendChild(particle);
    }
}

function createMorphingShapes() {
    const sections = document.querySelectorAll('section');
    sections.forEach((section, index) => {
        if (index % 2 === 0) { // Add to every other section
            const shape = document.createElement('div');
            shape.className = 'morphing-shape';
            shape.style.top = Math.random() * 50 + '%';
            shape.style.left = Math.random() * 50 + '%';
            section.style.position = 'relative';
            section.appendChild(shape);
        }
    });
}

function enhanceSkillCards() {
    const skillCards = document.querySelectorAll('.skill-card');
    skillCards.forEach(card => {
        // Add 3D tilt effect on mouse move
        card.addEventListener('mousemove', (e) => {
            const rect = card.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const rotateX = (y - centerY) / 10;
            const rotateY = (centerX - x) / 10;

            card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(20px)`;
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
        });
    });
}

function addHolographicText() {
    const mainTitle = document.querySelector('.hero-title');
    if (mainTitle) {
        mainTitle.classList.add('holographic-text');
        mainTitle.setAttribute('data-text', mainTitle.textContent);
    }

    const sectionTitles = document.querySelectorAll('.section-title');
    sectionTitles.forEach(title => {
        title.classList.add('holographic-text');
        title.setAttribute('data-text', title.textContent);
    });
}

function enhanceProjectCards() {
    const projectCards = document.querySelectorAll('.project-card');
    projectCards.forEach(card => {
        // Add parallax effect on scroll
        window.addEventListener('scroll', () => {
            const rect = card.getBoundingClientRect();
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;

            if (rect.top <= window.innerHeight && rect.bottom >= 0) {
                card.style.transform = `translateY(${rate}px)`;
            }
        });

        // Add magnetic effect
        card.addEventListener('mousemove', (e) => {
            const rect = card.getBoundingClientRect();
            const x = e.clientX - rect.left - rect.width / 2;
            const y = e.clientY - rect.top - rect.height / 2;

            card.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px) rotateX(${y * 0.05}deg) rotateY(${x * 0.05}deg)`;
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translate(0, 0) rotateX(0) rotateY(0)';
        });
    });
}

function createInteractiveBackground() {
    const canvas = document.createElement('canvas');
    canvas.id = 'interactive-bg';
    canvas.style.position = 'fixed';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    canvas.style.pointerEvents = 'none';
    canvas.style.zIndex = '-1';
    canvas.style.opacity = '0.3';
    document.body.appendChild(canvas);

    const ctx = canvas.getContext('2d');
    let animationId;

    function resizeCanvas() {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    }

    window.addEventListener('resize', resizeCanvas);
    resizeCanvas();

    const nodes = [];
    const nodeCount = 50;

    // Create nodes
    for (let i = 0; i < nodeCount; i++) {
        nodes.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            vx: (Math.random() - 0.5) * 2,
            vy: (Math.random() - 0.5) * 2,
            radius: Math.random() * 3 + 1
        });
    }

    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Update and draw nodes
        nodes.forEach(node => {
            node.x += node.vx;
            node.y += node.vy;

            // Bounce off edges
            if (node.x < 0 || node.x > canvas.width) node.vx *= -1;
            if (node.y < 0 || node.y > canvas.height) node.vy *= -1;

            // Draw node
            ctx.beginPath();
            ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
            ctx.fillStyle = '#00d4ff';
            ctx.fill();
        });

        // Draw connections
        nodes.forEach((node, i) => {
            nodes.slice(i + 1).forEach(otherNode => {
                const distance = Math.sqrt(
                    Math.pow(node.x - otherNode.x, 2) +
                    Math.pow(node.y - otherNode.y, 2)
                );

                if (distance < 150) {
                    ctx.beginPath();
                    ctx.moveTo(node.x, node.y);
                    ctx.lineTo(otherNode.x, otherNode.y);
                    ctx.strokeStyle = `rgba(0, 212, 255, ${1 - distance / 150})`;
                    ctx.lineWidth = 1;
                    ctx.stroke();
                }
            });
        });

        animationId = requestAnimationFrame(animate);
    }

    animate();

    // Pause animation when page is not visible
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            cancelAnimationFrame(animationId);
        } else {
            animate();
        }
    });
}

// Performance and mobile optimizations
function isMobile() {
    return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

function prefersReducedMotion() {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

function optimizeForDevice() {
    const isLowPowerDevice = isMobile();
    const reduceMotion = prefersReducedMotion();

    if (isLowPowerDevice || reduceMotion) {
        // Disable heavy animations and effects
        const particleContainers = document.querySelectorAll('.floating-particles');
        particleContainers.forEach(container => container.remove());

        const morphingShapes = document.querySelectorAll('.morphing-shape');
        morphingShapes.forEach(shape => shape.remove());

        const interactiveBg = document.getElementById('interactive-bg');
        if (interactiveBg) interactiveBg.remove();

        // Reduce matrix rain particles
        const matrixCanvas = document.getElementById('matrix-canvas');
        if (matrixCanvas && isLowPowerDevice) {
            const ctx = matrixCanvas.getContext('2d');
            // Reduce particle count for mobile
            window.matrixColumns = Math.floor(window.matrixColumns / 2);
        }
    }
}

function addIntersectionObserver() {
    // Lazy load animations when elements come into view
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '50px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe skill cards and project cards
    const animatedElements = document.querySelectorAll('.skill-card, .project-card, .section-title');
    animatedElements.forEach(el => observer.observe(el));
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Optimize scroll events
const optimizedScrollHandler = throttle(() => {
    // Handle scroll-based animations here
    const scrolled = window.pageYOffset;
    const parallaxElements = document.querySelectorAll('.ai-brain, .hero-visual');

    parallaxElements.forEach(element => {
        if (element.getBoundingClientRect().top < window.innerHeight) {
            const speed = 0.5;
            element.style.transform = `translateY(${scrolled * speed}px)`;
        }
    });
}, 16); // ~60fps

// Optimize resize events
const optimizedResizeHandler = debounce(() => {
    // Handle resize-based updates here
    const canvas = document.getElementById('cursor-trail');
    if (canvas) {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    }

    const matrixCanvas = document.getElementById('matrix-canvas');
    if (matrixCanvas) {
        matrixCanvas.width = window.innerWidth;
        matrixCanvas.height = window.innerHeight;
    }
}, 250);

// Add performance monitoring
function monitorPerformance() {
    if ('performance' in window) {
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                const loadTime = perfData.loadEventEnd - perfData.loadEventStart;

                if (loadTime > 3000) {
                    console.warn('Slow loading detected, consider optimizing');
                    // Could disable some effects here
                }
            }, 0);
        });
    }
}

// AI Neural Network Canvas Visualization
function createNeuralNetworkVisualization() {
    const canvas = document.getElementById('neural-network-canvas');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    let animationId;

    function resizeCanvas() {
        const heroVisual = document.querySelector('.hero-visual');
        if (heroVisual) {
            canvas.width = heroVisual.offsetWidth;
            canvas.height = heroVisual.offsetHeight;
        }
    }

    window.addEventListener('resize', resizeCanvas);
    resizeCanvas();

    // Neural network nodes
    const nodes = [];
    const connections = [];
    const nodeCount = 15;

    // Create nodes
    for (let i = 0; i < nodeCount; i++) {
        nodes.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            vx: (Math.random() - 0.5) * 0.5,
            vy: (Math.random() - 0.5) * 0.5,
            radius: Math.random() * 4 + 2,
            pulse: Math.random() * Math.PI * 2,
            pulseSpeed: 0.02 + Math.random() * 0.02
        });
    }

    // Create connections between nearby nodes
    function updateConnections() {
        connections.length = 0;
        nodes.forEach((node, i) => {
            nodes.slice(i + 1).forEach(otherNode => {
                const distance = Math.sqrt(
                    Math.pow(node.x - otherNode.x, 2) +
                    Math.pow(node.y - otherNode.y, 2)
                );

                if (distance < 120) {
                    connections.push({
                        from: node,
                        to: otherNode,
                        distance: distance,
                        strength: 1 - distance / 120
                    });
                }
            });
        });
    }

    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Update nodes
        nodes.forEach(node => {
            node.x += node.vx;
            node.y += node.vy;
            node.pulse += node.pulseSpeed;

            // Bounce off edges
            if (node.x < 0 || node.x > canvas.width) node.vx *= -1;
            if (node.y < 0 || node.y > canvas.height) node.vy *= -1;

            // Keep nodes in bounds
            node.x = Math.max(0, Math.min(canvas.width, node.x));
            node.y = Math.max(0, Math.min(canvas.height, node.y));
        });

        updateConnections();

        // Draw connections
        connections.forEach(connection => {
            const gradient = ctx.createLinearGradient(
                connection.from.x, connection.from.y,
                connection.to.x, connection.to.y
            );
            gradient.addColorStop(0, `rgba(0, 212, 255, ${connection.strength * 0.6})`);
            gradient.addColorStop(0.5, `rgba(255, 107, 107, ${connection.strength * 0.8})`);
            gradient.addColorStop(1, `rgba(255, 193, 7, ${connection.strength * 0.6})`);

            ctx.beginPath();
            ctx.moveTo(connection.from.x, connection.from.y);
            ctx.lineTo(connection.to.x, connection.to.y);
            ctx.strokeStyle = gradient;
            ctx.lineWidth = connection.strength * 2;
            ctx.stroke();
        });

        // Draw nodes
        nodes.forEach(node => {
            const pulseSize = node.radius + Math.sin(node.pulse) * 2;

            // Outer glow
            const gradient = ctx.createRadialGradient(
                node.x, node.y, 0,
                node.x, node.y, pulseSize * 2
            );
            gradient.addColorStop(0, 'rgba(0, 212, 255, 0.8)');
            gradient.addColorStop(0.5, 'rgba(255, 107, 107, 0.4)');
            gradient.addColorStop(1, 'transparent');

            ctx.beginPath();
            ctx.arc(node.x, node.y, pulseSize * 2, 0, Math.PI * 2);
            ctx.fillStyle = gradient;
            ctx.fill();

            // Core node
            ctx.beginPath();
            ctx.arc(node.x, node.y, pulseSize, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(255, 255, 255, ${0.8 + Math.sin(node.pulse) * 0.2})`;
            ctx.fill();

            // Inner core
            ctx.beginPath();
            ctx.arc(node.x, node.y, pulseSize * 0.5, 0, Math.PI * 2);
            ctx.fillStyle = 'rgba(0, 212, 255, 0.9)';
            ctx.fill();
        });

        animationId = requestAnimationFrame(animate);
    }

    animate();

    // Pause animation when page is not visible
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            cancelAnimationFrame(animationId);
        } else {
            animate();
        }
    });
}

// Initialize chatbot when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new AIAssistant();

    // Performance optimizations
    optimizeForDevice();
    addIntersectionObserver();
    monitorPerformance();

    // Add optimized event listeners
    window.addEventListener('scroll', optimizedScrollHandler, { passive: true });
    window.addEventListener('resize', optimizedResizeHandler);

    // Initialize advanced effects only if device can handle them
    if (!isMobile() && !prefersReducedMotion()) {
        createFloatingParticles();
        createMorphingShapes();
        createInteractiveBackground();
        createNeuralNetworkVisualization();
    }

    // Always initialize these (they're lightweight)
    enhanceSkillCards();
    addHolographicText();
    enhanceProjectCards();
});
